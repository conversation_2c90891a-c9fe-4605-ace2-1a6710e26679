package com.gwm.ailab.service.util;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import okhttp3.sse.EventSource;
import okhttp3.sse.EventSourceListener;
import okhttp3.sse.EventSources;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * OkHttp工具类
 * 提供GET、POST和EventStream调用功能
 *
 * <AUTHOR> Lab
 * @version 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OkHttpUtil {

    private final OkHttpClient okHttpClient;

    private static final MediaType JSON_MEDIA_TYPE = MediaType.get("application/json; charset=utf-8");

    /**
     * 执行GET请求
     *
     * @param url 请求URL
     * @return 响应字符串
     * @throws IOException IO异常
     */
    public String get(String url) throws IOException {
        return get(url, null);
    }

    /**
     * 执行GET请求（带请求头）
     *
     * @param url     请求URL
     * @param headers 请求头
     * @return 响应字符串
     * @throws IOException IO异常
     */
    public String get(String url, Map<String, String> headers) throws IOException {
        Request.Builder requestBuilder = new Request.Builder().url(url);

        // 添加请求头
        if (headers != null) {
            headers.forEach(requestBuilder::addHeader);
        }

        Request request = requestBuilder.build();

        try (Response response = okHttpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected response code: " + response.code());
            }
            ResponseBody body = response.body();
            return body != null ? body.string() : "";
        }
    }

    /**
     * 执行POST请求
     *
     * @param url  请求URL
     * @param json 请求体JSON
     * @return 响应字符串
     * @throws IOException IO异常
     */
    public String post(String url, String json) throws IOException {
        return post(url, json, null);
    }

    /**
     * 执行POST请求（带请求头）
     *
     * @param url     请求URL
     * @param json    请求体JSON
     * @param headers 请求头
     * @return 响应字符串
     * @throws IOException IO异常
     */
    public String post(String url, String json, Map<String, String> headers) throws IOException {
        RequestBody body = RequestBody.create(json, JSON_MEDIA_TYPE);
        Request.Builder requestBuilder = new Request.Builder()
                .url(url)
                .post(body);

        // 添加请求头
        if (headers != null) {
            headers.forEach(requestBuilder::addHeader);
        }

        Request request = requestBuilder.build();

        try (Response response = okHttpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected response code: " + response.code());
            }
            ResponseBody responseBody = response.body();
            return responseBody != null ? responseBody.string() : "";
        }
    }

    /**
     * 执行POST请求（JSONObject参数）
     *
     * @param url        请求URL
     * @param jsonObject 请求体JSONObject
     * @return 响应字符串
     * @throws IOException IO异常
     */
    public String post(String url, JSONObject jsonObject) throws IOException {
        return post(url, jsonObject.toJSONString(), null);
    }

    /**
     * 执行POST请求（JSONObject参数，带请求头）
     *
     * @param url        请求URL
     * @param jsonObject 请求体JSONObject
     * @param headers    请求头
     * @return 响应字符串
     * @throws IOException IO异常
     */
    public String post(String url, JSONObject jsonObject, Map<String, String> headers) throws IOException {
        return post(url, jsonObject.toJSONString(), headers);
    }

    /**
     * 异步GET请求
     *
     * @param url 请求URL
     * @return CompletableFuture<String>
     */
    public CompletableFuture<String> getAsync(String url) {
        return getAsync(url, null);
    }

    /**
     * 异步GET请求（带请求头）
     *
     * @param url     请求URL
     * @param headers 请求头
     * @return CompletableFuture<String>
     */
    public CompletableFuture<String> getAsync(String url, Map<String, String> headers) {
        CompletableFuture<String> future = new CompletableFuture<>();

        Request.Builder requestBuilder = new Request.Builder().url(url);
        if (headers != null) {
            headers.forEach(requestBuilder::addHeader);
        }

        Request request = requestBuilder.build();

        return getStringCompletableFuture(future, request);
    }

    /**
     * 异步POST请求
     *
     * @param url  请求URL
     * @param json 请求体JSON
     * @return CompletableFuture<String>
     */
    public CompletableFuture<String> postAsync(String url, String json) {
        return postAsync(url, json, null);
    }

    /**
     * 异步POST请求（带请求头）
     *
     * @param url     请求URL
     * @param json    请求体JSON
     * @param headers 请求头
     * @return CompletableFuture<String>
     */
    public CompletableFuture<String> postAsync(String url, String json, Map<String, String> headers) {
        CompletableFuture<String> future = new CompletableFuture<>();

        RequestBody body = RequestBody.create(json, JSON_MEDIA_TYPE);
        Request.Builder requestBuilder = new Request.Builder()
                .url(url)
                .post(body);

        if (headers != null) {
            headers.forEach(requestBuilder::addHeader);
        }

        Request request = requestBuilder.build();

        return getStringCompletableFuture(future, request);
    }

    private CompletableFuture<String> getStringCompletableFuture(CompletableFuture<String> future, Request request) {
        okHttpClient.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                future.completeExceptionally(e);
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                try (ResponseBody responseBody = response.body()) {
                    if (!response.isSuccessful()) {
                        future.completeExceptionally(new IOException("Unexpected response code: " + response.code()));
                        return;
                    }
                    String result = responseBody != null ? responseBody.string() : "";
                    future.complete(result);
                }
            }
        });

        return future;
    }

    /**
     * EventStream调用
     *
     * @param url       请求URL
     * @param json      请求体JSON
     * @param sseEmitter SSE发射器
     * @param headers   请求头
     */
    public void eventStream(String url, String json, SseEmitter sseEmitter, Map<String, String> headers) {
        RequestBody body = RequestBody.create(json, JSON_MEDIA_TYPE);
        Request.Builder requestBuilder = new Request.Builder()
                .url(url)
                .post(body)
                .addHeader("Accept", "text/event-stream")
                .addHeader("Cache-Control", "no-cache");

        // 添加自定义请求头
        if (headers != null) {
            headers.forEach(requestBuilder::addHeader);
        }

        Request request = requestBuilder.build();

        EventSourceListener listener = new EventSourceListener() {
            @Override
            public void onOpen(EventSource eventSource, Response response) {
                log.info("EventStream连接已打开: {}", url);
            }

            @Override
            public void onEvent(EventSource eventSource, String id, String type, String data) {
                try {
                    log.debug("接收到EventStream数据: id={}, type={}, data={}", id, type, data);
                    
                    // 发送数据到SSE客户端
                    if (data != null && !data.trim().isEmpty()) {
                        sseEmitter.send(SseEmitter.event()
                                .id(id)
                                .name(type)
                                .data(data));
                    }
                } catch (IOException e) {
                    log.error("发送SSE数据失败", e);
                    sseEmitter.completeWithError(e);
                }
            }

            @Override
            public void onClosed(EventSource eventSource) {
                log.info("EventStream连接已关闭: {}", url);
                sseEmitter.complete();
            }

            @Override
            public void onFailure(EventSource eventSource, Throwable t, Response response) {
                log.error("EventStream连接失败: {}", url, t);
                sseEmitter.completeWithError(t);
            }
        };

        EventSource eventSource = EventSources.createFactory(okHttpClient).newEventSource(request, listener);
        
        // 设置SSE超时处理
        sseEmitter.onTimeout(() -> {
            log.warn("SSE连接超时，关闭EventSource: {}", url);
            eventSource.cancel();
        });
        
        sseEmitter.onCompletion(() -> {
            log.info("SSE连接完成，关闭EventSource: {}", url);
            eventSource.cancel();
        });
    }

    /**
     * EventStream调用（JSONObject参数）
     *
     * @param url        请求URL
     * @param jsonObject 请求体JSONObject
     * @param sseEmitter SSE发射器
     * @param headers    请求头
     */
    public void eventStream(String url, JSONObject jsonObject, SseEmitter sseEmitter, Map<String, String> headers) {
        eventStream(url, jsonObject.toJSONString(), sseEmitter, headers);
    }

    /**
     * EventStream调用（无自定义请求头）
     *
     * @param url        请求URL
     * @param jsonObject 请求体JSONObject
     * @param sseEmitter SSE发射器
     */
    public void eventStream(String url, JSONObject jsonObject, SseEmitter sseEmitter) {
        eventStream(url, jsonObject.toJSONString(), sseEmitter, null);
    }
}
