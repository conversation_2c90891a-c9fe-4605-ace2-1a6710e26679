package com.gwm.ailab.service.controller;

import com.alibaba.fastjson2.JSONObject;
import com.gwm.ailab.service.common.ResponseResult;
import com.gwm.ailab.service.util.OkHttpUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.HashMap;
import java.util.Map;

/**
 * 生成式卡片控制器
 *
 * <AUTHOR> Lab
 * @version 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/card")
@Validated
@Tag(name = "服务卡片相关的API接口", description = "服务卡片相关的API接口")
@RequiredArgsConstructor
public class GenerativeCardController {

    private final OkHttpUtil okHttpUtil;

    @Value("${app.config.external-api.base-url:http://localhost:8081}")
    private String externalApiBaseUrl;

    @PostMapping(value = "/generate", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @Operation(summary = "服务卡片生成", description = "通过EventStream流式生成服务卡片")
    @ApiResponse(description = "服务卡片生成流式响应", content = @Content(mediaType = "text/event-stream"))
    public SseEmitter generate(@RequestBody JSONObject json) {
        log.info("开始生成服务卡片，请求参数: {}", json.toJSONString());

        // 创建SSE发射器，设置超时时间为5分钟
        SseEmitter sseEmitter = new SseEmitter(5 * 60 * 1000L);

        try {
            // 构建远程服务URL
            String remoteUrl = externalApiBaseUrl + "/api/generate";

            // 添加必要的请求头
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");
            headers.put("Accept", "text/event-stream");

            // 通过EventStream调用远程服务
            okHttpUtil.eventStream(remoteUrl, json, sseEmitter, headers);

            log.warn("EventStream连接已建立，开始接收数据流");

        } catch (Exception e) {
            log.error("生成服务卡片失败", e);
            sseEmitter.completeWithError(e);
        }

        return sseEmitter;
    }


}
